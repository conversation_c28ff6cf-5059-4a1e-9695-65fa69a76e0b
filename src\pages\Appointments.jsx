import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Calendar, 
  Clock, 
  User, 
  Scissors, 
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus
} from 'lucide-react'
import { db } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner, { CardSkeleton } from '../components/UI/LoadingSpinner'
import { formatDate, formatTime, formatPrice } from '../utils/helpers'
import { STATUS_LABELS, STATUS_COLORS, APPOINTMENT_STATUS } from '../utils/constants'
import toast from 'react-hot-toast'

const Appointments = () => {
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const { user, userProfile, isBarber } = useAuth()

  useEffect(() => {
    if (user) {
      fetchAppointments()
    }
  }, [user])

  const fetchAppointments = async () => {
    try {
      setLoading(true)
      
      let data, error
      if (isBarber()) {
        ({ data, error } = await db.appointments.getByBarberId(user.id))
      } else {
        ({ data, error } = await db.appointments.getByUserId(user.id))
      }
      
      if (error) {
        throw error
      }

      setAppointments(data || [])
    } catch (error) {
      console.error('Error fetching appointments:', error)
      toast.error('حدث خطأ أثناء تحميل المواعيد')
    } finally {
      setLoading(false)
    }
  }

  const updateAppointmentStatus = async (appointmentId, newStatus) => {
    try {
      const { error } = await db.appointments.update(appointmentId, { status: newStatus })
      
      if (error) {
        throw error
      }

      // Update local state
      setAppointments(prev => 
        prev.map(apt => 
          apt.id === appointmentId 
            ? { ...apt, status: newStatus }
            : apt
        )
      )

      const statusText = STATUS_LABELS[newStatus]
      toast.success(`تم تحديث حالة الموعد إلى: ${statusText}`)
    } catch (error) {
      console.error('Error updating appointment status:', error)
      toast.error('حدث خطأ أثناء تحديث حالة الموعد')
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case APPOINTMENT_STATUS.CONFIRMED:
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case APPOINTMENT_STATUS.REJECTED:
      case APPOINTMENT_STATUS.CANCELLED:
        return <XCircle className="h-5 w-5 text-red-600" />
      case APPOINTMENT_STATUS.COMPLETED:
        return <CheckCircle className="h-5 w-5 text-blue-600" />
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-600" />
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  const getFilterCounts = () => {
    return {
      all: appointments.length,
      pending: appointments.filter(apt => apt.status === APPOINTMENT_STATUS.PENDING).length,
      confirmed: appointments.filter(apt => apt.status === APPOINTMENT_STATUS.CONFIRMED).length,
      completed: appointments.filter(apt => apt.status === APPOINTMENT_STATUS.COMPLETED).length,
      cancelled: appointments.filter(apt => apt.status === APPOINTMENT_STATUS.CANCELLED).length,
    }
  }

  const counts = getFilterCounts()

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <LoadingSpinner size="lg" text="جاري تحميل المواعيد..." />
          </div>
          <CardSkeleton count={5} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2">
              {isBarber() ? 'مواعيد العملاء' : 'مواعيدي'}
            </h1>
            <p className="text-gray-600">
              {isBarber() 
                ? 'إدارة مواعيد العملاء وطلبات الحجز' 
                : 'تتبع مواعيدك وحالة الحجوزات'
              }
            </p>
          </div>
          
          {!isBarber() && (
            <Link
              to="/barbers"
              className="btn-primary flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="h-5 w-5" />
              <span>حجز موعد جديد</span>
            </Link>
          )}
        </div>

        {/* Filter Tabs */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                filter === 'all'
                  ? 'bg-black text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              الكل ({counts.all})
            </button>
            <button
              onClick={() => setFilter(APPOINTMENT_STATUS.PENDING)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                filter === APPOINTMENT_STATUS.PENDING
                  ? 'bg-yellow-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              قيد المراجعة ({counts.pending})
            </button>
            <button
              onClick={() => setFilter(APPOINTMENT_STATUS.CONFIRMED)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                filter === APPOINTMENT_STATUS.CONFIRMED
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              مؤكد ({counts.confirmed})
            </button>
            <button
              onClick={() => setFilter(APPOINTMENT_STATUS.COMPLETED)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                filter === APPOINTMENT_STATUS.COMPLETED
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              مكتمل ({counts.completed})
            </button>
            <button
              onClick={() => setFilter(APPOINTMENT_STATUS.CANCELLED)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                filter === APPOINTMENT_STATUS.CANCELLED
                  ? 'bg-red-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              ملغي ({counts.cancelled})
            </button>
          </div>
        </div>

        {/* Appointments List */}
        {filteredAppointments.length > 0 ? (
          <div className="space-y-6">
            {filteredAppointments.map((appointment) => (
              <div 
                key={appointment.id}
                className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  {/* Appointment Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 space-x-reverse mb-4">
                      <div className="bg-black text-white p-2 rounded-lg">
                        <Scissors className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-black">
                          {appointment.services?.name}
                        </h3>
                        <p className="text-gray-600">
                          {isBarber() 
                            ? `العميل: ${appointment.users?.full_name}`
                            : `الحلاق: ${appointment.barber?.full_name}`
                          }
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(appointment.appointment_date)}</span>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                        <Clock className="h-4 w-4" />
                        <span>{formatTime(appointment.appointment_time)}</span>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                        <span className="font-medium">{formatPrice(appointment.total_price)}</span>
                      </div>
                    </div>

                    {appointment.notes && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <p className="text-sm text-gray-700">
                          <strong>ملاحظات:</strong> {appointment.notes}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Status and Actions */}
                  <div className="flex flex-col items-end space-y-4">
                    {/* Status Badge */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {getStatusIcon(appointment.status)}
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${STATUS_COLORS[appointment.status]}`}>
                        {STATUS_LABELS[appointment.status]}
                      </span>
                    </div>

                    {/* Actions for Barbers */}
                    {isBarber() && appointment.status === APPOINTMENT_STATUS.PENDING && (
                      <div className="flex space-x-2 space-x-reverse">
                        <button
                          onClick={() => updateAppointmentStatus(appointment.id, APPOINTMENT_STATUS.CONFIRMED)}
                          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm"
                        >
                          قبول
                        </button>
                        <button
                          onClick={() => updateAppointmentStatus(appointment.id, APPOINTMENT_STATUS.REJECTED)}
                          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 text-sm"
                        >
                          رفض
                        </button>
                      </div>
                    )}

                    {/* Mark as Completed for Barbers */}
                    {isBarber() && appointment.status === APPOINTMENT_STATUS.CONFIRMED && (
                      <button
                        onClick={() => updateAppointmentStatus(appointment.id, APPOINTMENT_STATUS.COMPLETED)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm"
                      >
                        تم الانتهاء
                      </button>
                    )}

                    {/* Payment Status */}
                    {appointment.payments && appointment.payments.length > 0 && (
                      <div className="text-xs text-gray-500">
                        حالة الدفع: {appointment.payments[0].status === 'verified' ? 'تم التحقق' : 'في انتظار التحقق'}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-600 mb-2">
              {filter === 'all' 
                ? 'لا توجد مواعيد' 
                : `لا توجد مواعيد ${STATUS_LABELS[filter]}`
              }
            </h3>
            <p className="text-gray-500 mb-6">
              {isBarber() 
                ? 'لم يتم حجز أي مواعيد بعد'
                : 'لم تقم بحجز أي مواعيد بعد'
              }
            </p>
            {!isBarber() && (
              <Link
                to="/barbers"
                className="btn-primary inline-flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="h-5 w-5" />
                <span>احجز موعدك الأول</span>
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default Appointments

import { Link } from 'react-router-dom'
import { 
  Scissors, 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  Facebook,
  Instagram,
  Twitter
} from 'lucide-react'
import { APP_NAME } from '../../utils/constants'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-black text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="bg-white text-black p-2 rounded-lg">
                <Scissors className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold">{APP_NAME}</span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              صالون حلاقة عصري وفخم يقدم أفضل خدمات العناية بالشعر والذقن 
              بأحدث التقنيات وأمهر الحلاقين المحترفين.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              <a 
                href="#" 
                className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors duration-300"
                aria-label="فيسبوك"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors duration-300"
                aria-label="إنستغرام"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors duration-300"
                aria-label="تويتر"
              >
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/" 
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center space-x-2 space-x-reverse"
                >
                  <span>الرئيسية</span>
                </Link>
              </li>
              <li>
                <Link 
                  to="/services" 
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center space-x-2 space-x-reverse"
                >
                  <span>الخدمات</span>
                </Link>
              </li>
              <li>
                <Link 
                  to="/barbers" 
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center space-x-2 space-x-reverse"
                >
                  <span>الحلاقين</span>
                </Link>
              </li>
              <li>
                <Link 
                  to="/register" 
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center space-x-2 space-x-reverse"
                >
                  <span>إنشاء حساب</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold">معلومات التواصل</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3 space-x-reverse text-gray-300">
                <Phone className="h-5 w-5 text-white" />
                <span>01234567890</span>
              </li>
              <li className="flex items-center space-x-3 space-x-reverse text-gray-300">
                <Mail className="h-5 w-5 text-white" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-start space-x-3 space-x-reverse text-gray-300">
                <MapPin className="h-5 w-5 text-white mt-0.5" />
                <span>شارع التحرير، وسط البلد، القاهرة، مصر</span>
              </li>
            </ul>
          </div>

          {/* Working Hours */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold">مواعيد العمل</h3>
            <div className="space-y-2 text-gray-300">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Clock className="h-5 w-5 text-white" />
                <span className="font-medium">أوقات العمل</span>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>الأحد - الخميس</span>
                  <span>9:00 ص - 6:00 م</span>
                </div>
                <div className="flex justify-between">
                  <span>الجمعة</span>
                  <span>2:00 م - 6:00 م</span>
                </div>
                <div className="flex justify-between">
                  <span>السبت</span>
                  <span>9:00 ص - 3:00 م</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} {APP_NAME}. جميع الحقوق محفوظة.
            </div>
            <div className="flex space-x-6 space-x-reverse text-sm">
              <a 
                href="#" 
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                سياسة الخصوصية
              </a>
              <a 
                href="#" 
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                شروط الاستخدام
              </a>
              <a 
                href="#" 
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                اتصل بنا
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer

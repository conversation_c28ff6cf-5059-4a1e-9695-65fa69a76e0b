import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { 
  Calendar, 
  Clock, 
  User, 
  Scissors, 
  CreditCard,
  Upload,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'
import { db } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner, { ButtonSpinner } from '../components/UI/LoadingSpinner'
import { formatPrice, getNextAvailableDate, getAvailableTimeSlots } from '../utils/helpers'
import { TIME_SLOTS, SATURDAY_TIME_SLOTS, PAYMENT_METHODS } from '../utils/constants'
import toast from 'react-hot-toast'

// Validation schema
const schema = yup.object({
  service_id: yup.string().required('يرجى اختيار الخدمة'),
  appointment_date: yup.string().required('يرجى اختيار التاريخ'),
  appointment_time: yup.string().required('يرجى اختيار الوقت'),
  notes: yup.string(),
  vodafone_number: yup.string().required('رقم فودافون كاش مطلوب'),
  receipt_image: yup.mixed().required('صورة الإيصال مطلوبة')
})

const BookAppointment = () => {
  const { barberId } = useParams()
  const navigate = useNavigate()
  const { user, userProfile } = useAuth()
  
  const [barber, setBarber] = useState(null)
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [selectedService, setSelectedService] = useState(null)
  const [availableSlots, setAvailableSlots] = useState([])

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      appointment_date: getNextAvailableDate()
    }
  })

  const watchDate = watch('appointment_date')
  const watchServiceId = watch('service_id')

  useEffect(() => {
    if (barberId) {
      fetchBarberData()
    }
  }, [barberId])

  useEffect(() => {
    if (watchServiceId) {
      const service = services.find(s => s.id === watchServiceId)
      setSelectedService(service)
    }
  }, [watchServiceId, services])

  useEffect(() => {
    if (watchDate && barberId) {
      fetchAvailableSlots()
    }
  }, [watchDate, barberId])

  const fetchBarberData = async () => {
    try {
      setLoading(true)
      
      // Fetch barber info
      const { data: barberData, error: barberError } = await db.users.getById(barberId)
      if (barberError) throw barberError
      
      setBarber(barberData)
      
      // Fetch services
      const { data: servicesData, error: servicesError } = await db.services.getAll()
      if (servicesError) throw servicesError
      
      setServices(servicesData || [])
    } catch (error) {
      console.error('Error fetching barber data:', error)
      toast.error('حدث خطأ أثناء تحميل بيانات الحلاق')
      navigate('/barbers')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableSlots = async () => {
    try {
      // Get booked appointments for this date and barber
      const { data: appointments, error } = await db.appointments.getByBarberId(barberId)
      if (error) throw error

      const bookedSlots = appointments
        ?.filter(apt => apt.appointment_date === watchDate && apt.status !== 'cancelled')
        ?.map(apt => apt.appointment_time) || []

      const dayOfWeek = new Date(watchDate).getDay()
      const allSlots = dayOfWeek === 6 ? SATURDAY_TIME_SLOTS : TIME_SLOTS
      
      setAvailableSlots(allSlots.filter(slot => !bookedSlots.includes(slot)))
    } catch (error) {
      console.error('Error fetching available slots:', error)
      setAvailableSlots([])
    }
  }

  const onSubmit = async (data) => {
    try {
      setSubmitting(true)

      if (!selectedService) {
        toast.error('يرجى اختيار الخدمة')
        return
      }

      // Create appointment
      const appointmentData = {
        client_id: user.id,
        barber_id: barberId,
        service_id: data.service_id,
        appointment_date: data.appointment_date,
        appointment_time: data.appointment_time,
        notes: data.notes || null,
        total_price: selectedService.price,
        status: 'pending'
      }

      const { data: appointmentResult, error: appointmentError } = await db.appointments.create(appointmentData)
      if (appointmentError) throw appointmentError

      const appointment = appointmentResult[0]

      // Handle receipt upload (simplified for demo)
      let receiptUrl = null
      if (data.receipt_image && data.receipt_image[0]) {
        // In a real app, you would upload to Supabase Storage
        receiptUrl = 'demo-receipt-url'
      }

      // Create payment record
      const paymentData = {
        appointment_id: appointment.id,
        amount: selectedService.price,
        method: PAYMENT_METHODS.VODAFONE_CASH,
        vodafone_number: data.vodafone_number,
        receipt_image_url: receiptUrl,
        status: 'paid'
      }

      const { error: paymentError } = await db.payments.create(paymentData)
      if (paymentError) throw paymentError

      toast.success('تم حجز الموعد بنجاح! في انتظار موافقة الحلاق')
      navigate('/appointments')
    } catch (error) {
      console.error('Error booking appointment:', error)
      toast.error('حدث خطأ أثناء حجز الموعد')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <LoadingSpinner size="lg" text="جاري تحميل بيانات الحجز..." />
        </div>
      </div>
    )
  }

  if (!barber) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">الحلاق غير موجود</h1>
          <button
            onClick={() => navigate('/barbers')}
            className="btn-primary"
          >
            العودة إلى الحلاقين
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-black mb-2">حجز موعد</h1>
          <p className="text-gray-600">احجز موعدك مع {barber.full_name}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Barber Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-6">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="h-10 w-10 text-gray-500" />
                </div>
                <h3 className="text-xl font-bold text-black">{barber.full_name}</h3>
                <p className="text-gray-600">{barber.experience_years} سنوات خبرة</p>
              </div>

              {barber.bio && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">نبذة تعريفية</h4>
                  <p className="text-gray-600 text-sm">{barber.bio}</p>
                </div>
              )}

              {barber.specialties && barber.specialties.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">التخصصات</h4>
                  <div className="flex flex-wrap gap-2">
                    {barber.specialties.map((specialty, index) => (
                      <span 
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Service Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    اختر الخدمة
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {services.map((service) => (
                      <label key={service.id} className="relative">
                        <input
                          {...register('service_id')}
                          type="radio"
                          value={service.id}
                          className="sr-only"
                        />
                        <div className={`
                          p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                          ${watchServiceId === service.id 
                            ? 'border-black bg-black text-white' 
                            : 'border-gray-300 hover:border-gray-400'
                          }
                        `}>
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{service.name}</h4>
                            <span className="font-bold">{formatPrice(service.price)}</span>
                          </div>
                          <div className="flex items-center space-x-2 space-x-reverse text-sm opacity-75">
                            <Clock className="h-4 w-4" />
                            <span>{service.duration_minutes} دقيقة</span>
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                  {errors.service_id && (
                    <p className="mt-1 text-sm text-red-600">{errors.service_id.message}</p>
                  )}
                </div>

                {/* Date Selection */}
                <div>
                  <label htmlFor="appointment_date" className="block text-sm font-medium text-gray-700 mb-2">
                    اختر التاريخ
                  </label>
                  <div className="relative">
                    <Calendar className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      {...register('appointment_date')}
                      type="date"
                      id="appointment_date"
                      min={getNextAvailableDate()}
                      className="form-input pr-10"
                    />
                  </div>
                  {errors.appointment_date && (
                    <p className="mt-1 text-sm text-red-600">{errors.appointment_date.message}</p>
                  )}
                </div>

                {/* Time Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    اختر الوقت
                  </label>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-3">
                    {availableSlots.map((slot) => (
                      <label key={slot} className="relative">
                        <input
                          {...register('appointment_time')}
                          type="radio"
                          value={slot}
                          className="sr-only"
                        />
                        <div className={`
                          p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center
                          ${watch('appointment_time') === slot 
                            ? 'border-black bg-black text-white' 
                            : 'border-gray-300 hover:border-gray-400'
                          }
                        `}>
                          {slot}
                        </div>
                      </label>
                    ))}
                  </div>
                  {availableSlots.length === 0 && (
                    <p className="text-sm text-gray-500">لا توجد مواعيد متاحة في هذا التاريخ</p>
                  )}
                  {errors.appointment_time && (
                    <p className="mt-1 text-sm text-red-600">{errors.appointment_time.message}</p>
                  )}
                </div>

                {/* Notes */}
                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                    ملاحظات إضافية (اختياري)
                  </label>
                  <textarea
                    {...register('notes')}
                    id="notes"
                    rows={3}
                    className="form-input"
                    placeholder="أي ملاحظات أو طلبات خاصة..."
                  />
                </div>

                {/* Payment Section */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2 space-x-reverse">
                    <CreditCard className="h-5 w-5" />
                    <span>الدفع بفودافون كاش</span>
                  </h3>

                  {/* Vodafone Number */}
                  <div className="mb-4">
                    <label htmlFor="vodafone_number" className="block text-sm font-medium text-gray-700 mb-2">
                      رقم فودافون كاش
                    </label>
                    <input
                      {...register('vodafone_number')}
                      type="tel"
                      id="vodafone_number"
                      className="form-input"
                      placeholder="01xxxxxxxxx"
                    />
                    {errors.vodafone_number && (
                      <p className="mt-1 text-sm text-red-600">{errors.vodafone_number.message}</p>
                    )}
                  </div>

                  {/* Receipt Upload */}
                  <div className="mb-4">
                    <label htmlFor="receipt_image" className="block text-sm font-medium text-gray-700 mb-2">
                      صورة إيصال الدفع
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <input
                        {...register('receipt_image')}
                        type="file"
                        id="receipt_image"
                        accept="image/*"
                        className="hidden"
                      />
                      <label htmlFor="receipt_image" className="cursor-pointer">
                        <span className="text-sm text-gray-600">اضغط لرفع صورة الإيصال</span>
                      </label>
                    </div>
                    {errors.receipt_image && (
                      <p className="mt-1 text-sm text-red-600">{errors.receipt_image.message}</p>
                    )}
                  </div>

                  {/* Payment Instructions */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 className="font-medium text-blue-900 mb-2">تعليمات الدفع:</h4>
                    <ol className="text-sm text-blue-800 space-y-1">
                      <li>1. ادفع المبلغ المطلوب عبر فودافون كاش</li>
                      <li>2. التقط صورة لإيصال الدفع</li>
                      <li>3. ارفع صورة الإيصال أعلاه</li>
                      <li>4. اكمل الحجز</li>
                    </ol>
                  </div>

                  {/* Total */}
                  {selectedService && (
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">المجموع:</span>
                        <span className="text-2xl font-bold text-black">
                          {formatPrice(selectedService.price)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Submit Button */}
                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() => navigate('/barbers')}
                    className="flex-1 btn-secondary"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    disabled={submitting || availableSlots.length === 0}
                    className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    {submitting ? (
                      <>
                        <ButtonSpinner />
                        <span>جاري الحجز...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-5 w-5" />
                        <span>تأكيد الحجز</span>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookAppointment

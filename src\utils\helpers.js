import { DAYS_OF_WEEK, MONTHS, TIME_SLOTS, SATURDAY_TIME_SLOTS, VALIDATION_RULES, FILE_UPLOAD } from './constants'

// Date and Time Helpers
export const formatDate = (date) => {
  if (!date) return ''
  
  const d = new Date(date)
  const day = DAYS_OF_WEEK[d.getDay()]
  const dayNum = d.getDate()
  const month = MONTHS[d.getMonth()]
  const year = d.getFullYear()
  
  return `${day} ${dayNum} ${month} ${year}`
}

export const formatTime = (time) => {
  if (!time) return ''
  
  const [hours, minutes] = time.split(':')
  const hour12 = hours % 12 || 12
  const ampm = hours >= 12 ? 'م' : 'ص'
  
  return `${hour12}:${minutes} ${ampm}`
}

export const formatDateTime = (date, time) => {
  return `${formatDate(date)} - ${formatTime(time)}`
}

export const isToday = (date) => {
  const today = new Date()
  const checkDate = new Date(date)
  
  return today.toDateString() === checkDate.toDateString()
}

export const isFutureDate = (date) => {
  const today = new Date()
  const checkDate = new Date(date)
  
  today.setHours(0, 0, 0, 0)
  checkDate.setHours(0, 0, 0, 0)
  
  return checkDate >= today
}

export const getAvailableTimeSlots = (date, bookedSlots = []) => {
  const dayOfWeek = new Date(date).getDay()
  const slots = dayOfWeek === 6 ? SATURDAY_TIME_SLOTS : TIME_SLOTS // Saturday has shorter hours
  
  return slots.filter(slot => !bookedSlots.includes(slot))
}

export const getDateString = (date) => {
  return new Date(date).toISOString().split('T')[0]
}

export const addDays = (date, days) => {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}

export const getNextAvailableDate = () => {
  const tomorrow = addDays(new Date(), 1)
  return getDateString(tomorrow)
}

// Validation Helpers
export const validateEmail = (email) => {
  return VALIDATION_RULES.EMAIL.test(email)
}

export const validatePhone = (phone) => {
  return VALIDATION_RULES.PHONE.test(phone)
}

export const validatePassword = (password) => {
  return password && password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH
}

export const validateName = (name) => {
  return name && 
         name.length >= VALIDATION_RULES.NAME_MIN_LENGTH && 
         name.length <= VALIDATION_RULES.NAME_MAX_LENGTH
}

export const validateRequired = (value) => {
  return value && value.toString().trim().length > 0
}

// File Upload Helpers
export const validateFile = (file) => {
  const errors = []
  
  if (!file) {
    errors.push('يرجى اختيار ملف')
    return errors
  }
  
  if (file.size > FILE_UPLOAD.MAX_SIZE) {
    errors.push('حجم الملف كبير جداً (الحد الأقصى 5 ميجابايت)')
  }
  
  if (!FILE_UPLOAD.ALLOWED_TYPES.includes(file.type)) {
    errors.push('نوع الملف غير مدعوم (JPG, PNG فقط)')
  }
  
  return errors
}

export const generateFileName = (originalName, prefix = '') => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()
  
  return `${prefix}${timestamp}_${random}.${extension}`
}

// Price Helpers
export const formatPrice = (price) => {
  return `${price} جنيه`
}

export const calculateTotal = (items) => {
  return items.reduce((total, item) => total + (item.price || 0), 0)
}

// String Helpers
export const truncateText = (text, maxLength = 100) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  
  return text.substring(0, maxLength) + '...'
}

export const capitalizeFirst = (str) => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export const slugify = (text) => {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
}

// Array Helpers
export const groupBy = (array, key) => {
  return array.reduce((groups, item) => {
    const group = item[key]
    groups[group] = groups[group] || []
    groups[group].push(item)
    return groups
  }, {})
}

export const sortBy = (array, key, direction = 'asc') => {
  return [...array].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (direction === 'desc') {
      return bVal > aVal ? 1 : -1
    }
    
    return aVal > bVal ? 1 : -1
  })
}

export const filterBy = (array, filters) => {
  return array.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (value === '' || value === null || value === undefined) return true
      return item[key] === value
    })
  })
}

// Local Storage Helpers
export const setLocalStorage = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Error setting localStorage:', error)
  }
}

export const getLocalStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error getting localStorage:', error)
    return defaultValue
  }
}

export const removeLocalStorage = (key) => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing localStorage:', error)
  }
}

// URL Helpers
export const buildQueryString = (params) => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, value)
    }
  })
  
  return searchParams.toString()
}

export const parseQueryString = (queryString) => {
  const params = new URLSearchParams(queryString)
  const result = {}
  
  for (const [key, value] of params) {
    result[key] = value
  }
  
  return result
}

// Error Helpers
export const getErrorMessage = (error) => {
  if (typeof error === 'string') return error
  if (error?.message) return error.message
  if (error?.error_description) return error.error_description
  return 'حدث خطأ غير متوقع'
}

export const handleApiError = (error) => {
  console.error('API Error:', error)
  
  if (error?.code === 'PGRST301') {
    return 'لا توجد بيانات متاحة'
  }
  
  if (error?.code === 'PGRST116') {
    return 'غير مصرح لك بالوصول لهذه البيانات'
  }
  
  return getErrorMessage(error)
}

// Debounce Helper
export const debounce = (func, wait) => {
  let timeout
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Random Helpers
export const generateId = () => {
  return Math.random().toString(36).substring(2, 9)
}

export const getRandomColor = () => {
  const colors = [
    'bg-red-100 text-red-800',
    'bg-blue-100 text-blue-800',
    'bg-green-100 text-green-800',
    'bg-yellow-100 text-yellow-800',
    'bg-purple-100 text-purple-800',
    'bg-pink-100 text-pink-800',
    'bg-indigo-100 text-indigo-800'
  ]
  
  return colors[Math.floor(Math.random() * colors.length)]
}

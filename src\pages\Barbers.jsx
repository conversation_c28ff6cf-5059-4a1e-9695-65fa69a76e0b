import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { 
  User, 
  Star, 
  Clock, 
  Scissors, 
  Calendar,
  Award,
  MapPin
} from 'lucide-react'
import { db } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner, { CardSkeleton } from '../components/UI/LoadingSpinner'
import toast from 'react-hot-toast'

const Barbers = () => {
  const [barbers, setBarbers] = useState([])
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  useEffect(() => {
    fetchBarbers()
  }, [])

  const fetchBarbers = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.users.getBarbers()
      
      if (error) {
        throw error
      }

      setBarbers(data || [])
    } catch (error) {
      console.error('Error fetching barbers:', error)
      toast.error('حدث خطأ أثناء تحميل الحلاقين')
    } finally {
      setLoading(false)
    }
  }

  const renderRating = (rating) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-4 w-4 fill-yellow-400 text-yellow-400 opacity-50" />
      )
    }

    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      )
    }

    return stars
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <LoadingSpinner size="lg" text="جاري تحميل الحلاقين..." />
          </div>
          <CardSkeleton count={6} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-black text-white p-4 rounded-2xl shadow-xl">
              <User className="h-12 w-12" />
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-black mb-4">
            حلاقينا المحترفين
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            فريق من أمهر الحلاقين المحترفين ذوي الخبرة الواسعة 
            والمهارات العالية في فن الحلاقة والتجميل
          </p>
        </div>

        {/* Barbers Grid */}
        {barbers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {barbers.map((barber) => (
              <div 
                key={barber.id}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
              >
                {/* Barber Avatar */}
                <div className="relative">
                  <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    {barber.avatar_url ? (
                      <img 
                        src={barber.avatar_url} 
                        alt={barber.full_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="h-20 w-20 text-gray-500" />
                    )}
                  </div>
                  
                  {/* Experience Badge */}
                  <div className="absolute top-4 right-4 bg-black text-white px-3 py-1 rounded-full text-sm font-medium">
                    {barber.experience_years} سنوات خبرة
                  </div>
                </div>

                {/* Barber Info */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-black mb-2">
                    {barber.full_name}
                  </h3>

                  {/* Rating */}
                  <div className="flex items-center space-x-2 space-x-reverse mb-3">
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {renderRating(barber.rating || 0)}
                    </div>
                    <span className="text-sm text-gray-600">
                      ({barber.total_reviews || 0} تقييم)
                    </span>
                  </div>

                  {/* Bio */}
                  <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3">
                    {barber.bio || 'حلاق محترف متخصص في جميع أنواع قصات الشعر والحلاقة'}
                  </p>

                  {/* Specialties */}
                  {barber.specialties && barber.specialties.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">التخصصات:</h4>
                      <div className="flex flex-wrap gap-2">
                        {barber.specialties.slice(0, 3).map((specialty, index) => (
                          <span 
                            key={index}
                            className="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs"
                          >
                            {specialty}
                          </span>
                        ))}
                        {barber.specialties.length > 3 && (
                          <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-xs">
                            +{barber.specialties.length - 3} أخرى
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Services Count */}
                  <div className="flex items-center space-x-2 space-x-reverse text-gray-500 mb-4">
                    <Scissors className="h-4 w-4" />
                    <span className="text-sm">
                      {barber.barber_services?.length || 0} خدمة متاحة
                    </span>
                  </div>

                  {/* Book Button */}
                  {user ? (
                    <Link
                      to={`/book/${barber.id}`}
                      className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse font-medium"
                    >
                      <Calendar className="h-4 w-4" />
                      <span>احجز موعد</span>
                    </Link>
                  ) : (
                    <Link
                      to="/login"
                      className="w-full bg-gray-200 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-300 transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse font-medium"
                    >
                      <span>سجل دخولك للحجز</span>
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-600 mb-2">
              لا يوجد حلاقين متاحين حالياً
            </h3>
            <p className="text-gray-500">
              سيتم إضافة الحلاقين قريباً
            </p>
          </div>
        )}

        {/* Info Section */}
        <div className="mt-16 bg-white rounded-2xl shadow-xl p-8">
          <h2 className="text-3xl font-bold text-black text-center mb-8">
            لماذا تختار حلاقينا؟
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Award className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">خبرة واسعة</h3>
              <p className="text-gray-600">
                حلاقين محترفين بسنوات خبرة طويلة في مجال الحلاقة والتجميل
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Star className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">تقييمات عالية</h3>
              <p className="text-gray-600">
                حلاقين حاصلين على تقييمات ممتازة من العملاء السابقين
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Scissors className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">تخصصات متنوعة</h3>
              <p className="text-gray-600">
                كل حلاق متخصص في مجالات مختلفة لتلبية جميع احتياجاتك
              </p>
            </div>
          </div>
        </div>

        {/* Working Hours */}
        <div className="mt-8 bg-gradient-to-r from-black to-gray-900 text-white rounded-2xl p-8">
          <div className="text-center">
            <Clock className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-4">مواعيد العمل</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <h3 className="font-medium mb-2">الأحد - الخميس</h3>
                <p className="text-gray-300">9:00 ص - 6:00 م</p>
              </div>
              <div>
                <h3 className="font-medium mb-2">الجمعة</h3>
                <p className="text-gray-300">2:00 م - 6:00 م</p>
              </div>
              <div>
                <h3 className="font-medium mb-2">السبت</h3>
                <p className="text-gray-300">9:00 ص - 3:00 م</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Barbers

import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Sciss<PERSON>, Clock, Star, ArrowLeft } from 'lucide-react'
import { db } from '../lib/supabase'
import LoadingSpinner, { CardSkeleton } from '../components/UI/LoadingSpinner'
import { formatPrice } from '../utils/helpers'
import toast from 'react-hot-toast'

const Services = () => {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.services.getAll()
      
      if (error) {
        throw error
      }

      setServices(data || [])
    } catch (error) {
      console.error('Error fetching services:', error)
      toast.error('حدث خطأ أثناء تحميل الخدمات')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <LoadingSpinner size="lg" text="جاري تحميل الخدمات..." />
          </div>
          <CardSkeleton count={6} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-black text-white p-4 rounded-2xl shadow-xl">
              <Scissors className="h-12 w-12" />
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-black mb-4">
            خدماتنا المميزة
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            نقدم مجموعة شاملة من خدمات الحلاقة والعناية بالشعر والذقن 
            بأحدث التقنيات وأعلى معايير الجودة
          </p>
        </div>

        {/* Services Grid */}
        {services.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {services.map((service) => (
              <div 
                key={service.id}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
              >
                {/* Service Image */}
                <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  <Scissors className="h-16 w-16 text-gray-500 group-hover:text-gray-600 transition-colors duration-300" />
                </div>

                {/* Service Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-black mb-2 group-hover:text-gray-800 transition-colors duration-300">
                    {service.name}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Service Details */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-500">
                      <Clock className="h-4 w-4" />
                      <span className="text-sm">{service.duration_minutes} دقيقة</span>
                    </div>
                    
                    <div className="text-2xl font-bold text-black">
                      {formatPrice(service.price)}
                    </div>
                  </div>

                  {/* Book Button */}
                  <Link
                    to="/barbers"
                    className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse font-medium"
                  >
                    <span>احجز الآن</span>
                    <ArrowLeft className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Scissors className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-600 mb-2">
              لا توجد خدمات متاحة حالياً
            </h3>
            <p className="text-gray-500">
              سيتم إضافة الخدمات قريباً
            </p>
          </div>
        )}

        {/* Features Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-12">
          <h2 className="text-3xl font-bold text-black text-center mb-8">
            لماذا تختار خدماتنا؟
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Star className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">جودة عالية</h3>
              <p className="text-gray-600">
                نستخدم أفضل المنتجات والأدوات لضمان أعلى جودة خدمة
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Scissors className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">خبرة احترافية</h3>
              <p className="text-gray-600">
                فريق من أمهر الحلاقين المحترفين ذوي الخبرة الواسعة
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-black text-white p-4 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Clock className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-black mb-2">مواعيد مرنة</h3>
              <p className="text-gray-600">
                احجز موعدك في الوقت المناسب لك مع مرونة في التوقيتات
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-black to-gray-900 text-white rounded-2xl p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            جاهز لحجز موعدك؟
          </h2>
          
          <p className="text-xl mb-6 text-gray-300">
            اختر الحلاق المناسب واحجز موعدك الآن
          </p>
          
          <Link
            to="/barbers"
            className="bg-white text-black px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2 space-x-reverse"
          >
            <span>تصفح الحلاقين</span>
            <ArrowLeft className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Services

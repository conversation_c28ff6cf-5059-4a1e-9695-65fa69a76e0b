import { useState, useEffect } from 'react'
import { 
  Calendar, 
  Users, 
  DollarSign, 
  Clock,
  TrendingUp,
  Star,
  Scissors,
  CheckCircle
} from 'lucide-react'
import { db } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import LoadingSpinner from '../components/UI/LoadingSpinner'
import { formatPrice, formatDate } from '../utils/helpers'
import { APPOINTMENT_STATUS, STATUS_LABELS, STATUS_COLORS } from '../utils/constants'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalAppointments: 0,
    pendingAppointments: 0,
    confirmedAppointments: 0,
    completedAppointments: 0,
    totalRevenue: 0,
    todayAppointments: 0
  })
  const [recentAppointments, setRecentAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const { user, userProfile } = useAuth()

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch appointments
      const { data: appointments, error } = await db.appointments.getByBarberId(user.id)
      if (error) throw error

      const appointmentsList = appointments || []
      
      // Calculate stats
      const today = new Date().toISOString().split('T')[0]
      
      const newStats = {
        totalAppointments: appointmentsList.length,
        pendingAppointments: appointmentsList.filter(apt => apt.status === APPOINTMENT_STATUS.PENDING).length,
        confirmedAppointments: appointmentsList.filter(apt => apt.status === APPOINTMENT_STATUS.CONFIRMED).length,
        completedAppointments: appointmentsList.filter(apt => apt.status === APPOINTMENT_STATUS.COMPLETED).length,
        totalRevenue: appointmentsList
          .filter(apt => apt.status === APPOINTMENT_STATUS.COMPLETED)
          .reduce((sum, apt) => sum + parseFloat(apt.total_price || 0), 0),
        todayAppointments: appointmentsList.filter(apt => apt.appointment_date === today).length
      }

      setStats(newStats)
      
      // Get recent appointments (last 5)
      const recent = appointmentsList
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
      
      setRecentAppointments(recent)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <LoadingSpinner size="lg" text="جاري تحميل لوحة التحكم..." />
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'إجمالي المواعيد',
      value: stats.totalAppointments,
      icon: Calendar,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'مواعيد اليوم',
      value: stats.todayAppointments,
      icon: Clock,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      title: 'في انتظار الموافقة',
      value: stats.pendingAppointments,
      icon: Users,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600'
    },
    {
      title: 'إجمالي الأرباح',
      value: formatPrice(stats.totalRevenue),
      icon: DollarSign,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black mb-2">
            مرحباً، {userProfile?.full_name}
          </h1>
          <p className="text-gray-600">
            إليك نظرة عامة على أداءك اليوم
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat, index) => (
            <div 
              key={index}
              className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-black">
                    {stat.value}
                  </p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Appointments */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-black">المواعيد الأخيرة</h2>
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>

            {recentAppointments.length > 0 ? (
              <div className="space-y-4">
                {recentAppointments.map((appointment) => (
                  <div 
                    key={appointment.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="bg-black text-white p-2 rounded-lg">
                        <Scissors className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium text-black">
                          {appointment.users?.full_name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {appointment.services?.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(appointment.appointment_date)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[appointment.status]}`}>
                        {STATUS_LABELS[appointment.status]}
                      </span>
                      <p className="text-sm font-medium text-black mt-1">
                        {formatPrice(appointment.total_price)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600">لا توجد مواعيد حديثة</p>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-black">إحصائيات سريعة</h2>
              <TrendingUp className="h-5 w-5 text-gray-400" />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-900">مواعيد مكتملة</span>
                </div>
                <span className="text-xl font-bold text-green-600">
                  {stats.completedAppointments}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-900">مواعيد مؤكدة</span>
                </div>
                <span className="text-xl font-bold text-blue-600">
                  {stats.confirmedAppointments}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Clock className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium text-yellow-900">في انتظار الموافقة</span>
                </div>
                <span className="text-xl font-bold text-yellow-600">
                  {stats.pendingAppointments}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Star className="h-5 w-5 text-purple-600" />
                  <span className="font-medium text-purple-900">التقييم</span>
                </div>
                <span className="text-xl font-bold text-purple-600">
                  {userProfile?.rating?.toFixed(1) || '0.0'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Summary */}
        <div className="mt-8 bg-gradient-to-r from-black to-gray-900 text-white rounded-xl p-8">
          <div className="flex items-center space-x-4 space-x-reverse mb-6">
            <div className="bg-white text-black p-3 rounded-lg">
              <Scissors className="h-8 w-8" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">{userProfile?.full_name}</h2>
              <p className="text-gray-300">{userProfile?.experience_years} سنوات خبرة</p>
            </div>
          </div>

          {userProfile?.bio && (
            <p className="text-gray-300 mb-6">{userProfile.bio}</p>
          )}

          {userProfile?.specialties && userProfile.specialties.length > 0 && (
            <div>
              <h3 className="font-medium mb-3">التخصصات:</h3>
              <div className="flex flex-wrap gap-2">
                {userProfile.specialties.map((specialty, index) => (
                  <span 
                    key={index}
                    className="bg-white bg-opacity-20 px-3 py-1 rounded-lg text-sm"
                  >
                    {specialty}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard

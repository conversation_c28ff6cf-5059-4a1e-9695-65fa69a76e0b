import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = 'https://ibjumimqbjdcfeijplqr.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imliamp1bWltcWJqZGNmZWlqcGxxciIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNzMzMDk5OTE4LCJleHAiOjIwNDg2NzU5MTh9.placeholder-key-will-be-updated'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    // Disable email confirmation
    confirmSignUp: false
  }
})

// Auth helper functions
export const auth = {
  // Sign up new user
  signUp: async (email, password, userData = {}) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
          emailRedirectTo: undefined // Disable email confirmation
        }
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign in user
  signIn: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign out user
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  // Get current user
  getCurrentUser: () => {
    return supabase.auth.getUser()
  },

  // Get current session
  getCurrentSession: () => {
    return supabase.auth.getSession()
  }
}

// Database helper functions
export const db = {
  // Users
  users: {
    create: async (userData) => {
      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
      return { data, error }
    },

    getById: async (id) => {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single()
      return { data, error }
    },

    getByEmail: async (email) => {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single()
      return { data, error }
    },

    update: async (id, updates) => {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
      return { data, error }
    },

    getBarbers: async () => {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          barber_services (
            service_id,
            services (*)
          )
        `)
        .eq('role', 'barber')
        .eq('is_active', true)
      return { data, error }
    }
  },

  // Services
  services: {
    getAll: async () => {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('name')
      return { data, error }
    },

    getById: async (id) => {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('id', id)
        .single()
      return { data, error }
    }
  },

  // Appointments
  appointments: {
    create: async (appointmentData) => {
      const { data, error } = await supabase
        .from('appointments')
        .insert([appointmentData])
        .select(`
          *,
          users!appointments_client_id_fkey (*),
          barber:users!appointments_barber_id_fkey (*),
          services (*)
        `)
      return { data, error }
    },

    getByUserId: async (userId) => {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          users!appointments_client_id_fkey (*),
          barber:users!appointments_barber_id_fkey (*),
          services (*),
          payments (*)
        `)
        .eq('client_id', userId)
        .order('appointment_date', { ascending: false })
      return { data, error }
    },

    getByBarberId: async (barberId) => {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          users!appointments_client_id_fkey (*),
          services (*),
          payments (*)
        `)
        .eq('barber_id', barberId)
        .order('appointment_date', { ascending: false })
      return { data, error }
    },

    update: async (id, updates) => {
      const { data, error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          users!appointments_client_id_fkey (*),
          barber:users!appointments_barber_id_fkey (*),
          services (*),
          payments (*)
        `)
      return { data, error }
    }
  },

  // Payments
  payments: {
    create: async (paymentData) => {
      const { data, error } = await supabase
        .from('payments')
        .insert([paymentData])
        .select()
      return { data, error }
    },

    getByAppointmentId: async (appointmentId) => {
      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('appointment_id', appointmentId)
        .single()
      return { data, error }
    }
  }
}

// Storage helper functions
export const storage = {
  // Upload payment receipt
  uploadReceipt: async (file, fileName) => {
    try {
      const { data, error } = await supabase.storage
        .from('receipts')
        .upload(fileName, file)
      
      if (error) throw error
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from('receipts')
        .getPublicUrl(fileName)
      
      return { data: { ...data, publicUrl: urlData.publicUrl }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get receipt URL
  getReceiptUrl: (fileName) => {
    const { data } = supabase.storage
      .from('receipts')
      .getPublicUrl(fileName)
    return data.publicUrl
  }
}

export default supabase

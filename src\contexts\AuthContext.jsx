import { createContext, useContext, useEffect, useState } from 'react'
import { supabase, auth, db } from '../lib/supabase'
import toast from 'react-hot-toast'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [userProfile, setUserProfile] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          setUser(session.user)
          await fetchUserProfile(session.user.id)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setUser(session.user)
          await fetchUserProfile(session.user.id)
        } else {
          setUser(null)
          setUserProfile(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const fetchUserProfile = async (userId) => {
    try {
      const { data, error } = await db.users.getById(userId)
      if (error) {
        console.error('Error fetching user profile:', error)
        return
      }
      setUserProfile(data)
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  const signUp = async (email, password, userData) => {
    try {
      setLoading(true)
      
      // Sign up with Supabase Auth
      const { data: authData, error: authError } = await auth.signUp(email, password, userData)
      
      if (authError) {
        throw authError
      }

      if (authData.user) {
        // Create user profile in public.users table
        const profileData = {
          id: authData.user.id,
          email: email,
          full_name: userData.full_name,
          phone: userData.phone || null,
          role: userData.role || 'client',
          bio: userData.bio || null,
          experience_years: userData.experience_years || null,
          specialties: userData.specialties || null
        }

        const { error: profileError } = await db.users.create(profileData)
        
        if (profileError) {
          console.error('Error creating user profile:', profileError)
          // Don't throw here as auth user is already created
        }

        toast.success('تم إنشاء الحساب بنجاح!')
        return { data: authData, error: null }
      }
    } catch (error) {
      console.error('Sign up error:', error)
      toast.error(error.message || 'حدث خطأ أثناء إنشاء الحساب')
      return { data: null, error }
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email, password) => {
    try {
      setLoading(true)
      
      const { data, error } = await auth.signIn(email, password)
      
      if (error) {
        throw error
      }

      toast.success('تم تسجيل الدخول بنجاح!')
      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      toast.error(error.message || 'حدث خطأ أثناء تسجيل الدخول')
      return { data: null, error }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      
      const { error } = await auth.signOut()
      
      if (error) {
        throw error
      }

      setUser(null)
      setUserProfile(null)
      toast.success('تم تسجيل الخروج بنجاح!')
      return { error: null }
    } catch (error) {
      console.error('Sign out error:', error)
      toast.error(error.message || 'حدث خطأ أثناء تسجيل الخروج')
      return { error }
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (updates) => {
    try {
      if (!user) {
        throw new Error('لا يوجد مستخدم مسجل دخول')
      }

      const { data, error } = await db.users.update(user.id, updates)
      
      if (error) {
        throw error
      }

      setUserProfile(data[0])
      toast.success('تم تحديث الملف الشخصي بنجاح!')
      return { data, error: null }
    } catch (error) {
      console.error('Update profile error:', error)
      toast.error(error.message || 'حدث خطأ أثناء تحديث الملف الشخصي')
      return { data: null, error }
    }
  }

  const isBarber = () => {
    return userProfile?.role === 'barber'
  }

  const isClient = () => {
    return userProfile?.role === 'client'
  }

  const isAdmin = () => {
    return userProfile?.role === 'admin'
  }

  const value = {
    user,
    userProfile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    isBarber,
    isClient,
    isAdmin,
    fetchUserProfile
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
